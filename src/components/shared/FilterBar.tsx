import { FileText } from "lucide-react";
import { useEffect, useMemo, useState } from "react";
import { ScrollArea } from "../ui/scroll-area";
import { Badge } from "../ui/badge";

interface FilterConfig<T> {
  filterProperty: keyof T;
}

interface FilterBarProps<T> {
  items: T[];
  config: FilterConfig<T>;
  onResults: (results: T[]) => void;
  resetFilters?: boolean;
}

export function FilterBar<T>({ items, config, onResults, resetFilters = false }: FilterBarProps<T>) {

  const [selectedOption, setSelectedOption] = useState<string | null>(null);

  const optionsWithCounts = useMemo(() => {
    if (!items || items.length === 0) return [];

    const options = new Map()
    items.forEach((item) => {
      const value = item[config.filterProperty];
      if (value)
        options.set(value, (options.get(value) || 0) + 1);
    })

    return Array.from(options.entries())
      .map(([name, count]) => ({ name, count }))
      .sort((a, b) => a.name.localeCompare(b.name));

  }, [items])

  const handleOptionSelect = (optionName: string) => {
    setSelectedOption(optionName === selectedOption ? null : optionName);
  };

  useEffect(() => {
    if (resetFilters) {
      setSelectedOption(null);
    }
  }, [resetFilters]);

  useEffect(() => {
    if (selectedOption === null) {
      onResults(items);
    }
    else{
      const filtered = items.filter((item) => item[config.filterProperty] === selectedOption);
      onResults(filtered);
    }
  }, [selectedOption, items]);


  return (
    <div className="space-y-2">
      <div className="flex items-center justify-between">
        <h3 className="text-sm font-medium text-muted-foreground flex items-center gap-2">
          <FileText className="h-4 w-4" />
          Filter by Status
        </h3>
      </div>

      <ScrollArea className="w-full">
        <div className="flex gap-2 pb-2">
          {optionsWithCounts.map((option) => (
            <Badge
              key={option.name}
              variant={selectedOption === option.name ? "default" : "secondary"}
              className={`cursor-pointer whitespace-nowrap transition-all hover:scale-105 ${selectedOption === option.name
                ? "bg-primary text-primary-foreground shadow-md"
                : "hover:bg-secondary/80"
                }`}
              onClick={() => handleOptionSelect(option.name)}
            >
              {option.name} ({option.count})
            </Badge>
          ))}
        </div>
      </ScrollArea>
    </div>
  )
}